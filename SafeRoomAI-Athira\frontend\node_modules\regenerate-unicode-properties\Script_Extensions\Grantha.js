const set = require('regenerate')(0x1CD0, 0x20F0, 0x11350, 0x11357, 0x11FD3);
set.addRange(0x951, 0x952).addRange(0x964, 0x965).addRange(0xBE6, 0xBF3).addRange(0x1CD2, 0x1CD3).addRange(0x1CF2, 0x1CF4).addRange(0x1CF8, 0x1CF9).addRange(0x11300, 0x11303).addRange(0x11305, 0x1130C).addRange(0x1130F, 0x11310).addRange(0x11313, 0x11328).addRange(0x1132A, 0x11330).addRange(0x11332, 0x11333).addRange(0x11335, 0x11339).addRange(0x1133B, 0x11344).addRange(0x11347, 0x11348).addRange(0x1134B, 0x1134D).addRange(0x1135D, 0x11363).addRange(0x11366, 0x1136C).addRange(0x11370, 0x11374).addRange(0x11FD0, 0x11FD1);
exports.characters = set;
