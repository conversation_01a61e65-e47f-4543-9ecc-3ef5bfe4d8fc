"use strict";

// Generated by CoffeeScript 2.5.1
var _common, inlineStyleApplier, self, tools;

tools = require('../../tools');
_common = require('./_common');
module.exports = inlineStyleApplier = self = {
  applyTo: function applyTo(el, style) {
    var ret;
    ret = _common.getStyleTagsFor(style);

    if (style.marginLeft != null) {
      ret.before = tools.repeatString("&sp;", parseInt(style.marginLeft)) + ret.before;
    }

    if (style.marginRight != null) {
      ret.after += tools.repeatString("&sp;", parseInt(style.marginRight));
    }

    if (style.paddingLeft != null) {
      ret.before += tools.repeatString("&sp;", parseInt(style.paddingLeft));
    }

    if (style.paddingRight != null) {
      ret.after = tools.repeatString("&sp;", parseInt(style.paddingRight)) + ret.after;
    }

    return ret;
  }
};