"use strict";

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

// Generated by CoffeeScript 2.5.1
// Abstract Style Declaration
var _Declaration;

module.exports = _Declaration = function () {
  var self;

  var _Declaration = /*#__PURE__*/function () {
    function _Declaration(prop1, val) {
      _classCallCheck(this, _Declaration);

      this.prop = prop1;
      this.important = false;
      this.set(val);
    }

    _createClass(_Declaration, [{
      key: "get",
      value: function get() {
        return this._get();
      }
    }, {
      key: "_get",
      value: function _get() {
        return this.val;
      }
    }, {
      key: "_pickImportantClause",
      value: function _pickImportantClause(val) {
        if (self.importantClauseRx.test(String(val))) {
          this.important = true;
          return val.replace(self.importantClauseRx, '');
        } else {
          this.important = false;
          return val;
        }
      }
    }, {
      key: "set",
      value: function set(val) {
        val = self.sanitizeValue(val);
        val = this._pickImportantClause(val);
        val = val.trim();

        if (this._handleNullOrInherit(val)) {
          return this;
        }

        this._set(val);

        return this;
      }
    }, {
      key: "_set",
      value: function _set(val) {
        return this.val = val;
      }
    }, {
      key: "_handleNullOrInherit",
      value: function _handleNullOrInherit(val) {
        if (val === '') {
          this.val = '';
          return true;
        }

        if (val === 'inherit') {
          if (this.constructor.inheritAllowed) {
            this.val = 'inherit';
          } else {
            throw Error("Inherit is not allowed for `".concat(this.prop, "`"));
          }

          return true;
        } else {
          return false;
        }
      }
    }], [{
      key: "setOnto",
      value: function setOnto(declarations, prop, val) {
        var dec;

        if (!(dec = declarations[prop])) {
          return declarations[prop] = new this(prop, val);
        } else {
          return dec.set(val);
        }
      }
    }, {
      key: "sanitizeValue",
      value: function sanitizeValue(val) {
        return String(val).trim().replace(/[\s]+/g, ' ');
      }
    }]);

    return _Declaration;
  }();

  ;
  self = _Declaration;
  _Declaration.importantClauseRx = /(\s\!important)$/;
  _Declaration.inheritAllowed = false;
  return _Declaration;
}.call(void 0);